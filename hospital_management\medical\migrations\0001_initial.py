# Generated by Django 4.2.7 on 2025-08-31 13:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChuyenKhoa',
            fields=[
                ('ma_chuyen_khoa', models.AutoField(db_column='Ma_chuyen_khoa', primary_key=True, serialize=False)),
                ('ten_chuyen_khoa', models.CharField(db_column='Ten_chuyen_khoa', max_length=100)),
                ('mo_ta', models.TextField(blank=True, db_column='Mo_ta', null=True)),
            ],
            options={
                'verbose_name': 'Chuyen khoa',
                'verbose_name_plural': 'Chuyen khoa',
                'db_table': 'Chuyen_khoa',
            },
        ),
        migrations.CreateModel(
            name='CoSoYTe',
            fields=[
                ('ma_co_so', models.AutoField(db_column='Ma_co_so', primary_key=True, serialize=False)),
                ('ten_co_so', models.CharField(db_column='Ten_co_so', max_length=200)),
                ('loai_hinh', models.CharField(choices=[('Bệnh viện công', 'Bệnh viện công'), ('Bệnh viện tư', 'Bệnh viện tư'), ('Phòng khám', 'Phòng khám'), ('Trung tâm y tế', 'Trung tâm y tế')], db_column='Loai_hinh', max_length=50)),
                ('dia_chi', models.TextField(db_column='Dia_chi')),
                ('so_dien_thoai', models.CharField(db_column='So_dien_thoai', max_length=15)),
                ('email', models.EmailField(blank=True, db_column='Email', max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'Co so y te',
                'verbose_name_plural': 'Co so y te',
                'db_table': 'Co_so_y_te',
            },
        ),
        migrations.CreateModel(
            name='DichVu',
            fields=[
                ('ma_dich_vu', models.AutoField(db_column='Ma_dich_vu', primary_key=True, serialize=False)),
                ('ten_dich_vu', models.CharField(db_column='Ten_dich_vu', max_length=200)),
                ('loai_dich_vu', models.CharField(choices=[('Khám bệnh', 'Khám bệnh'), ('Xét nghiệm', 'Xét nghiệm'), ('Chẩn đoán hình ảnh', 'Chẩn đoán hình ảnh'), ('Thủ thuật', 'Thủ thuật'), ('Tư vấn từ xa', 'Tư vấn từ xa')], db_column='Loai_dich_vu', max_length=50)),
                ('gia_tien', models.DecimalField(db_column='Gia_tien', decimal_places=0, max_digits=10)),
                ('thoi_gian_kham', models.IntegerField(db_column='Thoi_gian_kham')),
                ('mo_ta', models.TextField(blank=True, db_column='Mo_ta', null=True)),
                ('ma_chuyen_khoa', models.ForeignKey(blank=True, db_column='Ma_chuyen_khoa', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dich_vu', to='medical.chuyenkhoa')),
                ('ma_co_so', models.ForeignKey(db_column='Ma_co_so', on_delete=django.db.models.deletion.CASCADE, related_name='dich_vu', to='medical.cosoyte')),
            ],
            options={
                'verbose_name': 'Dich vu',
                'verbose_name_plural': 'Dich vu',
                'db_table': 'Dich_vu',
            },
        ),
        migrations.AddField(
            model_name='chuyenkhoa',
            name='ma_co_so',
            field=models.ForeignKey(db_column='Ma_co_so', on_delete=django.db.models.deletion.CASCADE, related_name='chuyen_khoa', to='medical.cosoyte'),
        ),
        migrations.CreateModel(
            name='BacSi',
            fields=[
                ('ma_bac_si', models.AutoField(db_column='Ma_bac_si', primary_key=True, serialize=False)),
                ('ho_ten', models.CharField(db_column='Ho_ten', max_length=100)),
                ('gioi_tinh', models.CharField(choices=[('Nam', 'Nam'), ('Nữ', 'Nữ')], db_column='Gioi_tinh', max_length=10)),
                ('hoc_vi', models.CharField(choices=[('Bác sĩ', 'Bác sĩ'), ('Thạc sĩ', 'Thạc sĩ'), ('Tiến sĩ', 'Tiến sĩ'), ('Phó giáo sư', 'Phó giáo sư'), ('Giáo sư', 'Giáo sư')], db_column='Hoc_vi', max_length=20)),
                ('kinh_nghiem', models.IntegerField(db_column='Kinh_nghiem')),
                ('gioi_thieu', models.TextField(blank=True, db_column='Gioi_thieu', null=True)),
                ('ma_chuyen_khoa', models.ForeignKey(db_column='Ma_chuyen_khoa', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bac_si', to='medical.chuyenkhoa')),
                ('ma_co_so', models.ForeignKey(db_column='Ma_co_so', on_delete=django.db.models.deletion.CASCADE, related_name='bac_si', to='medical.cosoyte')),
                ('ma_nguoi_dung', models.OneToOneField(db_column='Ma_nguoi_dung', on_delete=django.db.models.deletion.CASCADE, related_name='bac_si', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Bac si',
                'verbose_name_plural': 'Bac si',
                'db_table': 'Bac_si',
            },
        ),
        migrations.AlterUniqueTogether(
            name='chuyenkhoa',
            unique_together={('ma_co_so', 'ten_chuyen_khoa')},
        ),
    ]
