# Generated by Django 4.2.7 on 2025-08-31 13:28

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Nguo<PERSON><PERSON><PERSON>',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('ma_nguoi_dung', models.AutoField(db_column='Ma_nguoi_dung', primary_key=True, serialize=False)),
                ('so_dien_thoai', models.Char<PERSON><PERSON>(db_column='So_dien_thoai', max_length=15, unique=True)),
                ('mat_khau', models.Char<PERSON>ield(db_column='Mat_khau', max_length=255)),
                ('vai_tro', models.Char<PERSON>ield(choices=[('Admin', 'Quản trị viên'), ('Bác sĩ', 'Bác sĩ'), ('Bệnh nhân', 'Bệnh nhân'), ('Nhân viên', 'Nhân viên y tế')], db_column='Vai_tro', max_length=20)),
                ('ngay_tao', models.DateTimeField(db_column='Ngay_tao', default=django.utils.timezone.now)),
                ('trang_thai', models.BooleanField(db_column='Trang_thai', default=True)),
                ('is_staff', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'Nguoi dung',
                'verbose_name_plural': 'Nguoi dung',
                'db_table': 'Nguoi_dung',
            },
        ),
    ]
