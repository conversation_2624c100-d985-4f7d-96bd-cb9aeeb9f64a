# Generated by Django 4.2.7 on 2025-08-31 13:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('appointments', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ThanhT<PERSON>',
            fields=[
                ('ma_thanh_toan', models.AutoField(db_column='Ma_thanh_toan', primary_key=True, serialize=False)),
                ('so_tien', models.DecimalField(db_column='So_tien', decimal_places=0, max_digits=10)),
                ('phuong_thuc', models.CharField(choices=[('Tien mat', 'Tien mat'), ('<PERSON>yen khoan', '<PERSON>yen khoan'), ('The tin dung', 'The tin dung'), ('Vi dien tu', 'Vi dien tu')], db_column='Phuong_thuc', max_length=50)),
                ('trang_thai', models.Char<PERSON>ield(choices=[('<PERSON><PERSON> thanh toan', '<PERSON><PERSON> thanh toan'), ('<PERSON> thanh toan', 'Da thanh toan'), ('<PERSON> hoan tien', '<PERSON> hoan tien')], db_column='Trang_thai', default='Chua thanh toan', max_length=20)),
                ('ma_giao_dich', models.CharField(blank=True, db_column='Ma_giao_dich', max_length=100, null=True)),
                ('thoi_gian_thanh_toan', models.DateTimeField(blank=True, db_column='Thoi_gian_thanh_toan', null=True)),
                ('ma_lich_hen', models.OneToOneField(db_column='Ma_lich_hen', on_delete=django.db.models.deletion.CASCADE, related_name='thanh_toan', to='appointments.lichhen')),
            ],
            options={
                'verbose_name': 'Thanh toan',
                'verbose_name_plural': 'Thanh toan',
                'db_table': 'Thanh_toan',
            },
        ),
    ]
