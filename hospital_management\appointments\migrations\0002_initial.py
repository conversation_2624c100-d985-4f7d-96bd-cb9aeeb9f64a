# Generated by Django 4.2.7 on 2025-08-31 13:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('medical', '0001_initial'),
        ('users', '0001_initial'),
        ('appointments', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='lichlamviec',
            name='ma_bac_si',
            field=models.ForeignKey(db_column='Ma_bac_si', on_delete=django.db.models.deletion.CASCADE, related_name='lich_lam_viec', to='medical.bacsi'),
        ),
        migrations.AddField(
            model_name='lichhen',
            name='ma_bac_si',
            field=models.ForeignKey(db_column='Ma_bac_si', on_delete=django.db.models.deletion.CASCADE, related_name='lich_hen', to='medical.bacsi'),
        ),
        migrations.AddField(
            model_name='lichhen',
            name='ma_benh_nhan',
            field=models.ForeignKey(db_column='Ma_benh_nhan', on_delete=django.db.models.deletion.CASCADE, related_name='lich_hen', to='users.benhnhan'),
        ),
        migrations.AddField(
            model_name='lichhen',
            name='ma_dich_vu',
            field=models.ForeignKey(db_column='Ma_dich_vu', on_delete=django.db.models.deletion.CASCADE, related_name='lich_hen', to='medical.dichvu'),
        ),
        migrations.AddField(
            model_name='lichhen',
            name='ma_lich',
            field=models.ForeignKey(db_column='Ma_lich', on_delete=django.db.models.deletion.CASCADE, related_name='lich_hen', to='appointments.lichlamviec'),
        ),
        migrations.AlterUniqueTogether(
            name='lichlamviec',
            unique_together={('ma_bac_si', 'ngay_lam_viec', 'gio_bat_dau')},
        ),
    ]
