Stack trace:
Frame         Function      Args
0007FFFF9DC0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8CC0) msys-2.0.dll+0x1FE8E
0007FFFF9DC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA098) msys-2.0.dll+0x67F9
0007FFFF9DC0  000210046832 (000210286019, 0007FFFF9C78, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DC0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DC0  000210068E24 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0A0  00021006A225 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF86C7E0000 ntdll.dll
7FF86BD40000 KERNEL32.DLL
7FF869F10000 KERNELBASE.dll
7FF8665A0000 apphelp.dll
7FF86B060000 USER32.dll
7FF869D80000 win32u.dll
7FF86B030000 GDI32.dll
7FF86A310000 gdi32full.dll
7FF869AC0000 msvcp_win.dll
7FF869970000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF86C030000 advapi32.dll
7FF86B530000 msvcrt.dll
7FF86AF80000 sechost.dll
7FF86B410000 RPCRT4.dll
7FF868F90000 CRYPTBASE.DLL
7FF869E70000 bcryptPrimitives.dll
7FF86BD00000 IMM32.DLL
