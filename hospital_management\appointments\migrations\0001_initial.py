# Generated by Django 4.2.7 on 2025-08-31 13:28

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Lich<PERSON><PERSON>',
            fields=[
                ('ma_lich_hen', models.AutoField(db_column='Ma_lich_hen', primary_key=True, serialize=False)),
                ('ngay_kham', models.DateField(db_column='Ngay_kham')),
                ('gio_kham', models.TimeField(db_column='Gio_kham')),
                ('so_thu_tu', models.IntegerField(db_column='So_thu_tu')),
                ('trang_thai', models.CharField(choices=[('Cho xac nhan', 'Cho xac nhan'), ('Da xac nhan', 'Da xac nhan'), ('<PERSON><PERSON> thanh', '<PERSON><PERSON> thanh'), ('<PERSON> huy', '<PERSON> huy')], db_column='Trang_thai', default='Cho xac nhan', max_length=20)),
                ('ghi_chu', models.TextField(blank=True, db_column='Ghi_chu', null=True)),
                ('ngay_tao', models.DateTimeField(db_column='Ngay_tao', default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name': 'Lich hen',
                'verbose_name_plural': 'Lich hen',
                'db_table': 'Lich_hen',
            },
        ),
        migrations.CreateModel(
            name='LichLamViec',
            fields=[
                ('ma_lich', models.AutoField(db_column='Ma_lich', primary_key=True, serialize=False)),
                ('ngay_lam_viec', models.DateField(db_column='Ngay_lam_viec')),
                ('gio_bat_dau', models.TimeField(db_column='Gio_bat_dau')),
                ('gio_ket_thuc', models.TimeField(db_column='Gio_ket_thuc')),
                ('so_luong_kham', models.IntegerField(db_column='So_luong_kham', default=20)),
                ('so_luong_da_dat', models.IntegerField(db_column='So_luong_da_dat', default=0)),
            ],
            options={
                'verbose_name': 'Lich lam viec',
                'verbose_name_plural': 'Lich lam viec',
                'db_table': 'Lich_lam_viec',
            },
        ),
        migrations.CreateModel(
            name='PhienTuVanTuXa',
            fields=[
                ('ma_phien', models.AutoField(db_column='Ma_phien', primary_key=True, serialize=False)),
                ('ma_cuoc_goi', models.CharField(blank=True, db_column='Ma_cuoc_goi', max_length=100, null=True, unique=True)),
                ('thoi_gian_bat_dau', models.DateTimeField(blank=True, db_column='Thoi_gian_bat_dau', null=True)),
                ('thoi_gian_ket_thuc', models.DateTimeField(blank=True, db_column='Thoi_gian_ket_thuc', null=True)),
                ('trang_thai', models.CharField(choices=[('Chua bat dau', 'Chua bat dau'), ('Dang dien ra', 'Dang dien ra'), ('Da ket thuc', 'Da ket thuc'), ('Da huy', 'Da huy')], db_column='Trang_thai', default='Chua bat dau', max_length=20)),
                ('ghi_chu_bac_si', models.TextField(blank=True, db_column='Ghi_chu_bac_si', null=True)),
                ('ma_lich_hen', models.OneToOneField(db_column='Ma_lich_hen', on_delete=django.db.models.deletion.CASCADE, related_name='phien_tu_van', to='appointments.lichhen')),
            ],
            options={
                'verbose_name': 'Phien tu van tu xa',
                'verbose_name_plural': 'Phien tu van tu xa',
                'db_table': 'Phien_tu_van_tu_xa',
            },
        ),
    ]
