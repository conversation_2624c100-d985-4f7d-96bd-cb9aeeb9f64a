# Generated by Django 4.2.7 on 2025-08-31 13:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='<PERSON><PERSON><PERSON><PERSON>',
            fields=[
                ('ma_benh_nhan', models.AutoField(db_column='Ma_benh_nhan', primary_key=True, serialize=False)),
                ('ho_ten', models.Char<PERSON>ield(db_column='Ho_ten', max_length=100)),
                ('ngay_sinh', models.DateField(db_column='Ngay_sinh')),
                ('gioi_tinh', models.CharField(choices=[('Nam', 'Nam'), ('Nữ', 'Nữ'), ('Khác', 'Khác')], db_column='Gioi_tinh', max_length=10)),
                ('cmnd_cccd', models.Char<PERSON>ield(blank=True, db_column='CMND_CCCD', max_length=20, null=True, unique=True)),
                ('so_bhyt', models.Char<PERSON>ield(blank=True, db_column='So_BHYT', max_length=20, null=True)),
                ('so_dien_thoai', models.CharField(db_column='So_dien_thoai', max_length=15)),
                ('email', models.EmailField(blank=True, db_column='Email', max_length=100, null=True)),
                ('dia_chi', models.TextField(db_column='Dia_chi')),
                ('ma_nguoi_dung', models.ForeignKey(db_column='Ma_nguoi_dung', on_delete=django.db.models.deletion.CASCADE, related_name='benh_nhan', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Benh nhan',
                'verbose_name_plural': 'Benh nhan',
                'db_table': 'Benh_nhan',
            },
        ),
    ]
